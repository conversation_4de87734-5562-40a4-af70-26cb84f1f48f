import { Navigation } from "@/components/navigation";
import { <PERSON> } from "@/components/hero";
import { Features } from "@/components/features";
// import { HowItWorks } from "@/components/how-it-works";
// import { WhySnapback } from "@/components/why-snapback";
// import { Comparison } from "@/components/comparison";
import { FAQ } from "@/components/faq";
// import { SystemRequirements } from "@/components/system-requirements";
// import { Privacy } from "@/components/privacy";
// import { Competitors } from "@/components/competitors";
import { FinalCTA } from "@/components/final-cta";

export default function Home() {
  return (
    <main className="min-h-screen bg-white">
      <Navigation />
      <Hero />
      <Features />
      {/* <HowItWorks /> */}
      {/* <WhySnapback /> */}
      {/* <Comparison /> */}
      <FAQ />
      {/* <SystemRequirements /> */}
      {/* <Privacy /> */}
      {/* <Competitors /> */}
      <FinalCTA />
    </main>
  );
}
