import { Button } from "@/components/ui/button";
import Image from "next/image";
import { TextAnimate } from "@/components/magicui/text-animate";
export function Hero() {
  return (
    <section className="relative overflow-hidden   px-6 py-24 sm:py-32 lg:px-8">
      <div className="mx-auto max-w-4xl text-center">
        <TextAnimate
          as="h1"
          animation="blurInUp"
          by="character"
          once
          className="mb-6 text-5xl font-extrabold tracking-tight text-gray-900 sm:text-6xl lg:text-8xl"
        >
          Save Your Perfect Workspace. Restore It Instantly
        </TextAnimate>

        <TextAnimate
          animation="blurInUp"
          by="character"
          once
          className="mb-12 text-xl leading-8 text-gray-600 max-w-3xl mx-auto"
        >
          <PERSON><PERSON><PERSON> remembers exactly how you work and brings it all back with a
          single shortcut.
        </TextAnimate>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {/* <Button
            size="lg"
            className="rounded-4xl bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 text-lg"
          >
            Download Now
          </Button> */}

          <Button
            size="lg"
            asChild
            className="h-14 bg-[#1f1f1f] rounded-4xl shadow-[0_0.78px_0.78px_rgba(0,0,0,0.004),0_1.92px_1.92px_rgba(0,0,0,0.01),0_3.63px_3.63px_rgba(0,0,0,0.024),0_6.35px_6.35px_rgba(0,0,0,0.04),0_11.04px_11.04px_rgba(0,0,0,0.07),0_20.25px_20.25px_rgba(0,0,0,0.125),0_40px_40px_rgba(0,0,0,0.25)] text-white px-6 py-4 text-lg font-semibold space-x-3"
          >
            <a href="" target="_blank" rel="noreferrer noopener">
              <div className="flex items-center gap-3">
                {/* Icon (simulated as a simple black box for now) */}
                <Image
                  src="/apple-logo.svg"
                  alt="Apple Logo"
                  width={20}
                  height={20}
                />

                {/* Text */}
                <span>Download for Mac</span>
              </div>
            </a>
          </Button>
        </div>
      </div>
    </section>
  );
}
