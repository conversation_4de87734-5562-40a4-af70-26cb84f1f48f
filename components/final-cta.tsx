import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";

export function FinalCTA() {
  return (
    <section className="py-24 px-6 lg:px-8">
      <div className="mx-auto max-w-7xl text-center text-white bg-gray-900 p-12 rounded-4xl">
        <h2 className="text-4xl font-bold mb-6">Ready to Stop Wasting Time?</h2>

        <p className="text-2xl font-semibold mb-4">
          Download Snapback and get back to work.
        </p>

        <p className="text-xl mb-12 opacity-90">
          Your perfect workspace is just one shortcut away.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <Button
            size="lg"
            className="bg-white text-gray-900 hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
          >
            <Download className="mr-2 h-5 w-5" />
            Download Now
          </Button>
        </div>
      </div>
    </section>
  );
}
