import { <PERSON>, CardContent } from "@/components/ui/card"
import { Monitor, Grid3X3, Zap } from "lucide-react"

const competitors = [
  {
    icon: Monitor,
    name: "macOS Spaces/Mission Control",
    description: "Create virtual desktops but don't save window arrangements",
  },
  {
    icon: Grid3X3,
    name: "Rectangle/Magnet",
    description: "Snap individual windows but can't save complete workspaces",
  },
  {
    icon: Zap,
    name: "<PERSON>nap<PERSON>",
    description: "Saves and restores your entire work environment with one keystroke",
    highlight: true,
  },
]

export function Competitors() {
  return (
    <section className="py-24 px-6 lg:px-8 bg-gray-50">
      <div className="mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-8">Snapback vs. Everything Else</h2>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {competitors.map((comp, index) => (
            <Card
              key={index}
              className={`border-0 shadow-lg ${comp.highlight ? "bg-gray-100 ring-2 ring-gray-300" : "bg-white"}`}
            >
              <CardContent className="p-6 text-center">
                <div className="mb-4 flex justify-center">
                  <div className={`rounded-lg p-3 ${comp.highlight ? "bg-gray-200" : "bg-gray-100"}`}>
                    <comp.icon className={`h-8 w-8 ${comp.highlight ? "text-gray-700" : "text-gray-600"}`} />
                  </div>
                </div>
                <h3 className={`font-bold mb-3 ${comp.highlight ? "text-gray-900" : "text-gray-900"}`}>{comp.name}</h3>
                <p className={`text-sm ${comp.highlight ? "text-gray-700" : "text-gray-600"}`}>{comp.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="border-0 shadow-xl bg-gray-900 text-white">
          <CardContent className="p-8 text-center">
            <p className="text-xl font-semibold">The only app that remembers your complete workspace context.</p>
          </CardContent>
        </Card>
      </div>
    </section>
  )
}
