import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Check, X } from "lucide-react"

const canDo = [
  {
    feature: "Save Complete Workspaces",
    description: "Capture all open apps, window positions, sizes, and arrangements",
  },
  { feature: "Instant Restoration", description: "Restore entire workspaces with a single keyboard shortcut" },
  { feature: "Multi-Display Support", description: "Handle complex multi-monitor setups perfectly" },
  { feature: "Smart App Launching", description: "Automatically launch closed applications during restoration" },
  { feature: "Custom Shortcuts", description: "Assign any keyboard combination to any workspace or function" },
  { feature: "Selective Window Management", description: "Choose exactly which windows to include in each workspace" },
  { feature: "Import/Export", description: "Share workspaces between machines or create backups" },
  { feature: "Window Snapping", description: "Optional precision window management with drag-to-snap" },
  { feature: "Conflict Avoidance", description: "Detect and avoid conflicts with macOS built-in features" },
  { feature: "Privacy Control", description: "Full control over which apps and data are captured" },
]

const cannotDo = [
  { feature: "Save Document State", description: "Cannot save unsaved work or document content within applications" },
  { feature: "Cross-App Data Sync", description: "Cannot synchronize data between different applications" },
  { feature: "System-Level Changes", description: "Cannot modify system settings, wallpapers, or macOS preferences" },
  { feature: "Network Configurations", description: "Cannot save or restore network settings or VPN connections" },
  { feature: "Application Settings", description: "Cannot backup or restore individual app preferences or settings" },
  { feature: "File System State", description: "Cannot save or restore file locations or folder structures" },
  { feature: "Running Processes", description: "Cannot save the internal state of running applications" },
  { feature: "Secure Content", description: "Cannot capture or restore password-protected or encrypted content" },
]

export function Comparison() {
  return (
    <section className="py-24 px-6 lg:px-8 bg-gray-50">
      <div className="mx-auto max-w-7xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">What Snapback Can Do vs Cannot Do</h2>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          <Card className="border-0 shadow-xl bg-white">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-3 text-2xl text-gray-800">
                <Check className="h-8 w-8 text-gray-600" />
                What Snapback CAN Do
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {canDo.map((item, index) => (
                <div key={index} className="border-b border-gray-100 pb-4 last:border-b-0">
                  <h4 className="font-semibold text-gray-900 mb-2">{item.feature}</h4>
                  <p className="text-gray-700 text-sm">{item.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card className="border-0 shadow-xl bg-white">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-3 text-2xl text-gray-800">
                <X className="h-8 w-8 text-gray-600" />
                What Snapback CANNOT Do
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {cannotDo.map((item, index) => (
                <div key={index} className="border-b border-gray-100 pb-4 last:border-b-0">
                  <h4 className="font-semibold text-gray-900 mb-2">{item.feature}</h4>
                  <p className="text-gray-700 text-sm">{item.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
