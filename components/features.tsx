import { <PERSON>ota<PERSON><PERSON>c<PERSON>, <PERSON>rid<PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import Image from "next/image";

const features = [
  {
    icon: RotateCcw,
    bg: "bg-[#F4665D]",
    title: "Save & Restore Window Layouts",
    description: "Snapback captures the full state of your workspace:",
    points: [
      "Window positions, sizes, and layering",
      "Works across multiple displays",
      "Restore everything instantly with a custom keyboard shortcut",
    ],
    footer:
      "Whether you're coding, designing, writing, or presenting get back to your flow in seconds.",
    image: "/features/workspaces.png",
  },
  {
    icon: Grid3X3,
    bg: "bg-[#09F8C1]",
    title: "Built-In Window Management",
    description:
      "Snapback doubles as a powerful, minimal window manager. Snap apps to halves, thirds, corners — or create your own grid.",
    points: ["Intuitive snapping", "Keyboard-first workflow", "Lightning fast"],
    footer: "You don't need a second app Snapback does it all.",
    image: "/features/window-management.png",
  },
  {
    icon: Brain,
    bg: "bg-[#6D34F3]",
    title: "Smarter Multitasking",
    description: "Create and save custom layouts for different tasks:",
    points: [
      '"Coding" layout for VSCode, Terminal, and Browser',
      '"Design" layout for Figma, Safari, and Slack',
      '"Presentation" layout for fullscreen Keynote and notes',
    ],
    footer: "Switch between them with ease your workspace adapts to you.",
    image: "/features/layouts.png",
  },
];

export function Features() {
  return (
    <section id="features" className="py-24 px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="space-y-24">
          {features.map((feature, index) => {
            const isEven = index % 2 === 0;
            return (
              <div
                key={index}
                className={`grid lg:grid-cols-2 gap-12 items-start ${
                  isEven ? "" : "lg:grid-flow-col-dense"
                }`}
              >
                {/* Content */}
                <div className={isEven ? "" : "lg:col-start-2"}>
                  <div className="flex items-start gap-6">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="rounded-lg bg-gray-100 p-3">
                          <feature.icon className="h-6 w-6 text-gray-600" />
                        </div>
                        <h3 className="text-3xl font-bold text-gray-900 ">
                          {feature.title}
                        </h3>
                      </div>

                      <p className="text-lg text-gray-700 mb-6">
                        {feature.description}
                      </p>
                      <ul className="space-y-3 mb-6">
                        {feature.points.map((point, pointIndex) => (
                          <li
                            key={pointIndex}
                            className="flex items-start gap-3"
                          >
                            <span className="text-gray-400 font-bold">•</span>
                            <span className="text-gray-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                      {feature.footer && (
                        <p className="text-gray-600 italic">{feature.footer}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Image */}
                <div className={isEven ? "lg:col-start-2" : "lg:col-start-1"}>
                  <div className="relative">
                    {/* <img
                      src={feature.image || "/placeholder.svg"}
                      alt={feature.title}
                      className="w-full h-auto rounded-lg shadow-lg border border-gray-200"
                    /> */}
                    {/* <Image
                      src={feature.image}
                      alt={feature.title}
                      width={600}
                      quality={100}
                      height={400}
                      className="w-full h-auto rounded-xl "
                    /> */}
                    <div
                      className={`w-full h-[600px]  rounded-4xl shadow-lg border ${feature.bg}`}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
