import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";

const faqs = [
  {
    question:
      "What is the difference between <PERSON>napback and Other Window Managers?",
    answer:
      "Think of Snapback as a workspace manager that also includes window snapping. Other window managers are primarily focused on window snapping and lack the workspace management features.",
  },
  {
    question: 'What\'s a "workspace" in Snapback?',
    answer:
      "A saved snapshot of your desktop which apps are open, where windows are positioned, their sizes. Like a bookmark for your entire work setup.",
  },

  {
    question: "Can I save workspaces within macOS Spaces?",
    answer:
      "No, Snapback does not support saving workspaces within Spaces. API limitations prevent us from accessing window information within Spaces.",
  },
  {
    question: "Does Snapback replace macOS Spaces?",
    answer:
      "No, Snapback complements Spaces. Spaces are great for virtual desktops, but <PERSON><PERSON><PERSON> saves the exact window layout within each Space.",
  },
  {
    question: "Do I need the window snapping features?",
    answer:
      "Nope! Workspace save/restore is the main feature. Window snapping is optional and can be disabled completely.",
  },
  {
    question: "What macOS versions work?",
    answer:
      "macOS 14.6+ (Sonoma) is required. Snapback does not work on earlier versions.",
  },
  {
    question: "What permissions does it need?",
    answer:
      "Just Accessibility permissions to control window positions. Standard macOS permission, nothing sensitive.",
  },
  {
    question: "Will it conflict with Other Window Managers?",
    answer:
      "No conflicts. Snapback can coexist with other window managers, if you have duplicate hotkeys, you can change the hotkeys or you can disable its snapping features entirely.",
  },
  {
    question: "Does it work with all apps?",
    answer:
      "Works with most apps. Some apps may have restrictions on window movement or resizing, but most work fine.",
  },
  {
    question: "Is my data private?",
    answer:
      "100% local storage. No cloud, no analytics, no data transmission. Your workspace configs stay on your Mac.",
  },
  {
    question: "What if I replace monitors, will my workspaces still work?",
    answer:
      "Yes, Snapback is designed to handle monitor changes, including monitor resolution changes and monitor reordering. It will automatically adjust window positions to fit your new setup, but be aware that changing the main display can cause issues.",
  },
  {
    question: "What if I wanna share workspaces between machines?",
    answer:
      "Currently, Snapback workspaces are stored locally on your machine. However, you can manually export and import workspace configurations if needed, in the future we plan to add support for cloud syncing.",
  },
  {
    question: "Do I need to buy a license for each machine I use it on?",
    answer:
      "No, once you purchase a license, it's valid for all machines you use it on.",
  },
];

export function FAQ() {
  return (
    <section id="faq" className="py-24 px-6 lg:px-8">
      <div className="mx-auto max-w-4xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">FAQ</h2>
        </div>

        <Accordion type="single" collapsible className="space-y-4">
          {faqs.map((faq, index) => (
            <AccordionItem
              key={index}
              value={`item-${index}`}
              className="border border-gray-200 rounded-lg px-6"
            >
              <AccordionTrigger className="text-left font-semibold text-gray-900 hover:no-underline">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-gray-700 pb-6">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
}
