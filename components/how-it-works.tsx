import { Card, CardContent } from "@/components/ui/card";
import { Settings, Save, Zap } from "lucide-react";

const steps = [
  {
    icon: Settings,
    number: "1",
    title: "Arrange your perfect workspace",
    description: "Position your apps exactly how you want them",
  },
  {
    icon: Save,
    number: "2",
    title: "Save with ⌘⇧S",
    description: "Give it a name and custom shortcut",
  },
  {
    icon: Zap,
    number: "3",
    title: "Restore instantly",
    description: "Press your shortcut from anywhere in macOS",
  },
];

export function HowItWorks() {
  return (
    <section id="how-it-works" className="py-24 px-6 lg:px-8 bg-gray-50">
      <div className="mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Start Once. Snap Back Forever.
          </h2>
          <p className="text-xl text-gray-700 font-semibold">
            Set it up once. Snap back in an instant.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <Card key={index} className="border-0 shadow-lg bg-white">
              <CardContent className="p-8 text-center">
                <div className="mb-6 flex justify-center">
                  <div className="rounded-full bg-gray-900 text-white w-12 h-12 flex items-center justify-center text-xl font-bold">
                    <step.icon className="h-8 w-8" />
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {step.title}
                </h3>
                <p className="text-gray-600">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 mb-6">
            Stop wasting time rearranging windows.
            <br />
            Try Snapback today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors">
              Download Now
            </button>
            <button className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-3 rounded-lg font-semibold text-lg transition-colors">
              Join the Beta
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
