import { Card, CardContent } from "@/components/ui/card"
import { Shield, Lock, Database, Eye } from "lucide-react"

const privacyFeatures = [
  { icon: Database, text: "100% local storage using macOS UserDefaults" },
  { icon: Shield, text: "No cloud transmission or analytics" },
  { icon: Eye, text: "Only captures window positions, never document content" },
  { icon: Lock, text: "Standard macOS Accessibility permissions (one-time setup)" },
]

export function Privacy() {
  return (
    <section id="privacy" className="py-24 px-6 lg:px-8">
      <div className="mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">Privacy & Security</h2>
          <p className="text-2xl font-semibold text-gray-700">Your data stays on your Mac.</p>
        </div>

        <div className="grid md:grid-cols-2 gap-6 mb-12">
          {privacyFeatures.map((feature, index) => (
            <Card key={index} className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="rounded-lg bg-gray-100 p-3">
                    <feature.icon className="h-6 w-6 text-gray-600" />
                  </div>
                  <span className="text-lg text-gray-700">{feature.text}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="border-0 shadow-xl bg-gray-50">
          <CardContent className="p-8 text-center">
            <p className="text-lg text-gray-700 font-medium">Future CloudKit sync will be completely optional.</p>
          </CardContent>
        </Card>
      </div>
    </section>
  )
}
