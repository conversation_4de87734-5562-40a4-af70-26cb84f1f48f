import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, HardDrive, Shield, Cpu } from "lucide-react"

const requirements = [
  { icon: Laptop, label: "Minimum", value: "macOS 10.15+ (Catalina or later)" },
  { icon: Cpu, label: "Recommended", value: "macOS 12+ (Monterey or later)" },
  { icon: HardDrive, label: "Architecture", value: "Intel & Apple Silicon (M1/M2/M3) Macs" },
  { icon: HardDrive, label: "Storage", value: "50MB" },
  { icon: Shield, label: "Permissions", value: "Accessibility access (one-time setup)" },
]

export function SystemRequirements() {
  return (
    <section id="requirements" className="py-24 px-6 lg:px-8 bg-gray-50">
      <div className="mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">System Requirements</h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {requirements.map((req, index) => (
            <Card key={index} className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="rounded-lg bg-gray-100 p-3 flex-shrink-0">
                    <req.icon className="h-6 w-6 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 mb-2">{req.label}</h3>
                    <p className="text-gray-700 text-sm">{req.value}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
