import { Card, CardContent } from "@/components/ui/card"
import { Clock, Code, Palette, GraduationCap, Users } from "lucide-react"

const benefits = [
  { icon: Code, text: "Developers juggling multiple projects" },
  { icon: Palette, text: "Designers switching between clients" },
  { icon: GraduationCap, text: "Students organizing study sessions" },
  { icon: Users, text: "Anyone who values their time" },
]

export function WhySnapback() {
  return (
    <section className="py-24 px-6 lg:px-8">
      <div className="mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">Why Snapback?</h2>
          <p className="text-2xl font-semibold text-gray-700 mb-4">Stop losing momentum to window management.</p>
        </div>

        <Card className="border-0 shadow-xl bg-gray-50 mb-12">
          <CardContent className="p-8 lg:p-12">
            <div className="text-center mb-8">
              <Clock className="h-16 w-16 text-gray-500 mx-auto mb-4" />
              <p className="text-xl text-gray-700 leading-relaxed">
                Every time you switch projects, you waste <span className="font-bold text-gray-900">10+ minutes</span>{" "}
                recreating your workspace. Snapback eliminates that friction — so you can focus on what matters.
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Perfect for:</h3>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {benefits.map((benefit, index) => (
            <Card key={index} className="border-0 shadow-lg bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="rounded-lg bg-gray-100 p-3">
                    <benefit.icon className="h-6 w-6 text-gray-600" />
                  </div>
                  <span className="text-lg text-gray-700 font-medium">{benefit.text}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
